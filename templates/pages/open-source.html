<div class="open-source-portfolio">
  <!-- Last updated: {{lastUpdated}} | Projects: {{totalProjects}} -->

  <div class="portfolio-stats">
    <div class="stats-grid">
      {{#each stats}}
      <div class="stat-item">
        <span class="stat-number">{{number}}</span>
        <span class="stat-label">{{label}}</span>
      </div>
      {{/each}}
    </div>

    <!-- Contributions Chart -->
    <div class="contributions-chart" id="contributionsChart" style="display: none;">
      <div class="chart-header">
        <h3 class="chart-title">Contribution Timeline</h3>
        <p class="chart-subtitle">Years of open source contributions</p>
      </div>
      <div class="chart-container">
        <canvas id="contributionsCanvas"></canvas>
      </div>
    </div>
  </div>

  <div class="projects-section">
    <h2 class="section-header">
      <span class="section-title">Current Projects</span>
    </h2>
    <div class="projects-grid">
      {{#each currentProjects}}
      {{>project-card}}
      {{/each}}
    </div>
  </div>

  <div class="projects-section">
    <h2 class="section-header">
      <span class="section-title">Past Projects</span>
    </h2>
    <div class="projects-grid">
      {{#each pastProjects}}
      {{>project-card}}
      {{/each}}
    </div>
  </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// GitHub stats data (embedded from server)
const statsData = {{statsDataJson}};

// Initialize chart when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  renderContributionsChart();
});

// Render contributions chart
function renderContributionsChart() {
  if (!statsData || !statsData.yearly_contributions) {
    return;
  }

  const chartContainer = document.getElementById('contributionsChart');
  chartContainer.style.display = 'block';

  const ctx = document.getElementById('contributionsCanvas').getContext('2d');

  // Prepare data for the chart
  const yearlyData = statsData.yearly_contributions.sort((a, b) => a.year - b.year);
  const years = yearlyData.map(item => item.year);
  const contributions = yearlyData.map(item => item.totalContributions);

  // Create gradient
  const gradient = ctx.createLinearGradient(0, 0, 0, 300);
  gradient.addColorStop(0, 'rgba(37, 99, 235, 0.3)');
  gradient.addColorStop(1, 'rgba(37, 99, 235, 0.05)');

  new Chart(ctx, {
    type: 'line',
    data: {
      labels: years,
      datasets: [{
        label: 'Contributions',
        data: contributions,
        borderColor: '#2563eb',
        backgroundColor: gradient,
        borderWidth: 3,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: '#2563eb',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 5,
        pointHoverRadius: 7,
        pointHoverBackgroundColor: '#1d4ed8',
        pointHoverBorderColor: '#ffffff',
        pointHoverBorderWidth: 3
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          backgroundColor: 'rgba(15, 23, 42, 0.9)',
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          borderColor: '#2563eb',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: false,
          callbacks: {
            title: function(context) {
              return `Year ${context[0].label}`;
            },
            label: function(context) {
              return `${context.parsed.y.toLocaleString()} contributions`;
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            display: false
          },
          ticks: {
            color: '#64748b',
            font: {
              size: 12,
              weight: '500'
            }
          }
        },
        y: {
          beginAtZero: true,
          grid: {
            color: 'rgba(226, 232, 240, 0.5)',
            drawBorder: false
          },
          ticks: {
            color: '#64748b',
            font: {
              size: 12
            },
            callback: function(value) {
              return value.toLocaleString();
            }
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index'
      },
      elements: {
        point: {
          hoverRadius: 8
        }
      }
    }
  });
}
</script>
