.open-source-portfolio {
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.portfolio-stats {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 3rem 2rem;
  border-radius: 1rem;
  margin: 3rem 0;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.stat-item {
  text-align: center;
}

.stat-item {
  position: relative;
}

.stat-item::before {
  content: "📊";
  font-size: 1.5rem;
  display: block;
  margin-bottom: 0.5rem;
}

.stat-item:nth-child(1)::before { content: "💻"; }
.stat-item:nth-child(2)::before { content: "📦"; }
.stat-item:nth-child(3)::before { content: "🏢"; }
.stat-item:nth-child(4)::before { content: "⏰"; }

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  display: block;
  margin-bottom: 0.5rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
  font-size: 1rem;
  color: rgba(255,255,255,0.9);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-weight: 500;
}

.projects-section {
  margin: 3rem 0;
}

.projects-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
  margin: 2rem 0;
}

.project-card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.project-header {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1rem;
}

.project-logo {
  width: 48px;
  height: 48px;
  border-radius: 0.5rem;
  background: #f1f5f9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 1.25rem;
  color: #2563eb;
}

.project-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.5rem;
}

.project-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  color: #1e293b;
}

.project-meta {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 0.5rem;
}

.project-badge {
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-active {
  background: #dcfce7;
  color: #166534;
}

.badge-discontinued {
  background: #fef3c7;
  color: #92400e;
}

.badge-framework {
  background: #dbeafe;
  color: #1e40af;
}

.badge-library {
  background: #f3e8ff;
  color: #7c3aed;
}

.badge-organization {
  background: #fce7f3;
  color: #be185d;
}

.project-description {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.project-links {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
  color: #495057;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.project-link:hover {
  background: #e9ecef;
  border-color: #adb5bd;
  color: #212529;
  text-decoration: none;
}

.highlights-section {
  margin-top: 1rem;
  padding-top: 1rem;
}

.highlights-list {
  list-style: none;
  padding: 0;
  margin: 0.5rem 0 0 0;
}

.highlights-list li {
  color: #495057;
  position: relative;
  padding-left: 2.5rem;
}

.highlights-list li:before {
  content: "✓";
  position: absolute;
  left: -0.5rem;
  color: #10b981;
  font-weight: bold;
}

.highlighted-repos {
  margin-top: 1rem;
  padding-top: 1rem;
}

.highlighted-repos h4 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 1.5rem 0;
  color: #374151;
}

.repos-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

.repo-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.repo-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.repo-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.repo-name {
  font-weight: 600;
  color: #1e293b;
  text-decoration: none;
  font-size: 18px;
}

.repo-name:hover {
  color: #3b82f6;
  text-decoration: none;
}

.repo-stats {
  font-size: 16px;
  color: #64748b;
  margin-bottom: 0.75rem;
  font-weight: 500;
}

.repo-description {
  font-size: 15px;
  color: #475569;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.repo-links {
  margin-top: 0.5rem;
  display: flex;
  gap: 0.5rem;
}

.repo-link {
  font-size: 14px;
  padding: 0.5rem 1rem;
  background: #e2e8f0;
  color: #475569;
  text-decoration: none;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
  font-weight: 500;
}

.repo-link:hover {
  background: #cbd5e1;
  text-decoration: none;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .project-header {
    flex-direction: column;
    align-items: flex-start;
  }
  .project-links {
    flex-direction: column;
  }
}
