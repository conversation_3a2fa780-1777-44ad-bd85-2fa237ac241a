{"generated_at": "2025-08-11T00:00:00Z", "projects": [{"id": "elixir-drops", "name": "Eli<PERSON>r Drops", "category": "current", "type": "library", "description": "My latest project that brings dry-rb and rom-rb goodies to the Elixir ecosystem.", "technologies": ["<PERSON><PERSON><PERSON>"], "status": "active", "role": "Creator & Maintainer", "year_started": 2023, "links": {"website": "https://solnic.dev/introducing-elixir-drops", "github": "https://github.com/solnic/drops"}, "logo": null, "highlights": ["Schema-based data validation and casting to Elixir", "High-level Relation API on top of Ecto", "Composable operations for business logic with built-in observability framework", "Standalone Inflector library"], "highlightedRepoNames": ["solnic/drops", "solnic/drops_relation", "solnic/drops_operation", "solnic/drops_inflector"]}], "past_projects": [{"id": "hanami", "name": "Hanami", "category": "past", "type": "framework", "description": "A Ruby application framework with a modern component-based architecture. <PERSON> was part of the core team from 2018-2023, working on its 2.0 release which uses various dry-rb libraries as its foundation as well as rom-rb as the default \"model layer\".", "technologies": ["<PERSON>"], "status": "active", "role": "Former Core Team Member", "year_started": 2018, "year_ended": 2023, "links": {"website": "https://hanamirb.org", "github": "https://github.com/hanami/hanami"}, "logo": null, "highlights": ["Modern component-based architecture", "Uses dry-rb and rom-rb as foundation", "2.0 release development", "Core team member 2018-2023"], "highlightedRepoNames": ["hanami/hanami", "hanami/view", "hanami/cli"]}, {"id": "dry-rb", "name": "dry-rb", "category": "past", "type": "organization", "description": "The organization was created by <PERSON> in 2015. I was thinking about doing a similar thing so I decided to contribute and started working on a couple of gems under this organization. The projects aim to be a modern take on solving common problems. Libraries are small and simple to understand with a great focus on reusability.", "technologies": ["<PERSON>"], "status": "active", "role": "Former Core Contributor", "year_started": 2015, "year_ended": 2023, "links": {"website": "http://dry-rb.org/", "github": "https://github.com/dry-rb"}, "logo": "dry-rb-round-150x150.png", "highlights": ["Modern approach to common problems", "Small, reusable libraries", "Great focus on simplicity"], "highlightedRepoNames": ["dry-rb/dry-validation", "dry-rb/dry-schema", "dry-rb/dry-types", "dry-rb/dry-system"], "sub_projects": [{"name": "dry-validation", "description": "validation library with type-safe schemas and rules", "github": "https://github.com/dry-rb/dry-validation"}, {"name": "dry-schema", "description": "schema DSL that was originally provided by dry-validation", "github": "https://github.com/dry-rb/dry-schema"}, {"name": "dry-types", "description": "a flexible \"type system\" for Ruby projects. Currently it's the foundation for other libraries, like rom-rb, dry-validation, hanami or reform", "github": "https://github.com/dry-rb/dry-types"}, {"name": "dry-struct", "description": "a virtus-like attributes API for POROs", "github": "https://github.com/dry-rb/dry-struct"}, {"name": "dry-logic", "description": "composable rule objects", "github": "https://github.com/dry-rb/dry-logic"}, {"name": "dry-system", "description": "a modern way of organizing Ruby applications using dependency injection as the architectural foundation", "github": "https://github.com/dry-rb/dry-system"}, {"name": "dry-auto_inject", "description": "container-agnostic auto-injection abstraction", "github": "https://github.com/dry-rb/dry-auto_inject"}, {"name": "dry-events", "description": "a simple pub/sub solution", "github": "https://github.com/dry-rb/dry-events"}, {"name": "dry-monitor", "description": "a set of abstractions that help with application monitoring", "github": "https://github.com/dry-rb/dry-events"}]}, {"id": "rom-rb", "name": "rom-rb", "category": "past", "type": "organization", "description": "The work on Ruby Object Mapper (rom-rb) initially started as an attempt to build the second major version of the DataMapper project; however, in 2014 I decided to take the project in a different direction and turn it into an FP/OO hybrid toolkit that simplifies working with the data using Ruby language. It consists of a small(ish) core and plenty of adapters and extensions.", "technologies": ["<PERSON>"], "status": "active", "role": "Former Creator & Lead Maintainer", "year_started": 2014, "year_ended": 2023, "links": {"website": "http://rom-rb.org/", "github": "https://github.com/rom-rb/rom", "blog_post": "https://solnic.codes/2014/10/23/ruby-object-mapper-reboot/"}, "logo": "rom_avatar_440x440-150x150.png", "highlights": ["FP/OO hybrid persistence and mapping toolkit", "Simplifies working with data", "Modular architecture with adapters"], "highlightedRepoNames": ["rom-rb/rom", "rom-rb/rom-sql", "rom-rb/rom-factory"], "sub_projects": [{"name": "rom-core", "description": "design and implementation of the core APIs, such as ROM::Gateway, ROM::Relation, ROM::Command or ROM::Mapper", "github": "https://github.com/rom-rb/rom/tree/master/core"}, {"name": "rom-repository", "description": "design and implementation of the repository component", "github": "https://github.com/rom-rb/rom/tree/master/repository"}, {"name": "rom-changeset", "description": "design and implementation of the changeset component", "github": "https://github.com/rom-rb/rom/tree/master/changeset"}, {"name": "rom-sql", "description": "the official SQL adapter", "github": "https://github.com/rom-rb/rom-sql"}, {"name": "rom-elasticsearch", "description": "the official Elasticsearch adapter", "github": "https://github.com/rom-rb/rom-elasticsearch"}, {"name": "rom-factory", "description": "the official data generator and \"factory\" toolkit", "github": "https://github.com/rom-rb/rom-factory"}]}, {"id": "transproc", "name": "Transproc", "category": "past", "type": "library", "description": "Transproc is a Ruby gem which provides an API for functional composition of arbitrary Proc-like objects. It introduced the left-to-right >> function composition operator, which is now considered as a potential addition to Ruby's core.", "technologies": ["<PERSON>"], "status": "active", "role": "Former Creator & Maintainer", "year_started": 2014, "year_ended": 2023, "links": {"github": "https://github.com/solnic/transproc", "ruby_feature_request": "https://bugs.ruby-lang.org/issues/6284"}, "logo": null, "highlights": ["Functional composition API", "Left-to-right >> operator", "Considered for Ruby core", "Data transformation foundation for rom-rb"], "highlightedRepoNames": ["solnic/transproc"]}, {"id": "datamapper", "name": "DataMapper", "category": "past", "type": "framework", "description": "DataMapper was a Ruby ORM that was part of the default stack of the Merb framework. I started helping with the project in late 2008 and eventually joined the core team in 2010. I mostly focused on working on the Property API (which later on was extracted into a separate library called Virtus), on-going maintenance, bug fixing, user support and handling releases.", "technologies": ["<PERSON>"], "status": "discontinued", "role": "Core Team Member", "year_started": 2008, "year_ended": 2014, "links": {"github": "https://github.com/datamapper"}, "logo": null, "highlights": ["Part of Merb framework stack", "Property API development", "Core team member from 2010", "Foundation for later projects"], "highlightedRepoNames": ["datamapper/dm-core", "datamapper/dm-types", "datamapper/dm-validations"]}, {"id": "virtus", "name": "Virtus", "category": "past", "type": "library", "description": "Virtus is a project that started as an extraction of the DataMapper Property API back in 2011. Eventually it has become very popular and made typed struct-like objects in Ruby a thing and inspired people to build their own solutions too. There were also many other gems that started using Virtus under the hood for coercions, like Representable or Grape.", "technologies": ["<PERSON>"], "status": "discontinued", "role": "Creator & Maintainer", "year_started": 2011, "year_ended": 2019, "links": {"github": "https://github.com/solnic/virtus"}, "logo": null, "highlights": ["Extracted from DataMapper Property API", "Made typed structs popular in Ruby", "Inspired many other solutions", "Used by <PERSON>resent<PERSON>, <PERSON><PERSON><PERSON>, and others"], "highlightedRepoNames": ["solnic/virtus"]}, {"id": "coercible", "name": "Co<PERSON><PERSON>", "category": "past", "type": "library", "description": "Coercible is the coercion backend extracted from Virtus which provides a set of generic coercions for most common data types like numbers, dates etc.", "technologies": ["<PERSON>"], "status": "discontinued", "role": "Creator & Maintainer", "year_started": 2011, "year_ended": 2019, "links": {"github": "https://github.com/solnic/coercible"}, "logo": null, "highlights": ["Extracted from Virtus", "Generic coercions for common data types", "Backend for type coercion"]}], "stats": {"total_projects": 8, "active_projects": 1, "discontinued_projects": 7, "years_contributing": 17, "technologies": ["<PERSON>", "<PERSON><PERSON><PERSON>"], "roles": ["Creator", "Maintainer", "Core Team Member", "Lead Maintainer", "Core Contributor"]}}